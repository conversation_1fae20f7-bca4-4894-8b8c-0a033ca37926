# DigiviceColor - 数码宝贝虚拟宠物游戏

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Platform](https://img.shields.io/badge/Platform-Windows%20%7C%20macOS%20%7C%20Linux%20%7C%20Android%20%7C%20iOS%20%7C%20WebAssembly-blue.svg)](https://github.com/axmolengine/axmol)
[![Engine](https://img.shields.io/badge/Engine-Axmol-green.svg)](https://github.com/axmolengine/axmol)

这是一个基于 **C++** 和 **Axmol 引擎** 构建的跨平台数码宝贝虚拟宠物游戏。该项目旨在重现经典的数码宝贝电子宠物设备体验，并融入现代化的图形界面和交互功能，为玩家提供沉浸式的数码宝贝养成体验。

## 🎯 项目概述

### 主要功能和目标
- **虚拟宠物养成**：实现完整的数码宝贝养成系统
- **进化系统**：多样化的进化路线和条件
- **互动体验**：丰富的宠物互动和小游戏
- **跨平台支持**：支持Windows、macOS、Linux、Android、iOS和WebAssembly
- **现代化界面**：基于Axmol引擎的高质量2D图形渲染

### 技术栈
- **编程语言**：C++17
- **游戏引擎**：Axmol Engine (Cocos2d-x分支)
- **构建系统**：CMake 3.20+
- **图形API**：OpenGL ES 3.0
- **音频引擎**：内置音频引擎 (USE_AUDIO_ENGINE)
- **调试工具**：Inspector扩展
- **版本控制**：Git

### 项目架构
采用经典的游戏引擎架构模式：
- **应用层**：AppDelegate负责应用生命周期管理
- **场景层**：MainScene实现游戏主要逻辑和状态管理
- **渲染层**：基于Axmol的2D渲染管线
- **输入层**：多平台输入事件处理（触摸、键盘、鼠标）
- **资源层**：统一的资源管理和加载系统

## 📁 项目结构分析

### 目录结构概览
```
DigiviceColor/
├── 📄 CMakeLists.txt         # 主CMake构建配置文件
├── 📁 Source/                # C++源代码目录
│   ├── AppDelegate.cpp       # 应用程序委托实现
│   ├── AppDelegate.h         # 应用程序委托头文件
│   ├── MainScene.cpp         # 主场景实现
│   └── MainScene.h           # 主场景头文件
├── 📁 Content/               # 游戏资源目录
│   ├── 🖼️ CloseNormal.png     # UI按钮资源
│   ├── 🖼️ CloseSelected.png   # UI按钮选中状态
│   ├── 🖼️ HelloWorld.png      # 示例图像资源
│   ├── 🖼️ startMenu.png       # 开始菜单背景
│   ├── 📁 fonts/             # 字体资源目录
│   │   ├── Marker Felt.ttf   # 游戏标题字体
│   │   └── arial.ttf         # 通用字体
│   └── 📁 res/               # 其他资源文件
├── 📁 cmake/                 # CMake模块和脚本
│   └── modules/              # 自定义CMake模块
├── 📁 proj.win32/            # Windows平台特定文件
│   ├── main.cpp              # Windows入口点
│   ├── main.h                # Windows主头文件
│   └── resource.h            # Windows资源定义
├── 📁 proj.android/          # Android平台项目
│   ├── app/                  # Android应用配置
│   ├── build.gradle          # Gradle构建脚本
│   ├── settings.gradle       # Gradle设置
│   └── AndroidManifest.xml   # Android清单文件
├── 📁 proj.ios_mac/          # iOS/macOS平台项目
│   ├── ios/                  # iOS特定配置
│   ├── mac/                  # macOS特定配置
│   └── main.cpp              # macOS入口点
├── 📁 proj.linux/            # Linux平台项目
│   └── main.cpp              # Linux入口点
├── 📁 proj.wasm/             # WebAssembly平台项目
│   └── main.cpp              # WASM入口点
├── 📁 proj.winrt/            # Windows Runtime平台项目
├── 📄 build.bat              # Windows快速构建脚本
├── 📄 run.bat                # Windows快速运行脚本
├── 📄 run.bat.in             # 运行脚本模板
└── 📁 build/                 # CMake构建输出目录
    ├── bin/                  # 可执行文件输出
    ├── lib/                  # 库文件输出
    └── engine/               # Axmol引擎构建文件
```

### 核心模块分析

#### 1. 应用程序入口 (AppDelegate)
<augment_code_snippet path="DigiviceColor/Source/AppDelegate.h" mode="EXCERPT">
````cpp
class AppDelegate : private ax::Application
{
public:
    AppDelegate();
    ~AppDelegate() override;
    void initGLContextAttrs() override;
    bool applicationDidFinishLaunching() override;
    void applicationDidEnterBackground() override;
    void applicationWillEnterForeground() override;
};
````
</augment_code_snippet>

**功能特性**：
- **生命周期管理**：处理应用启动、后台、前台切换
- **OpenGL上下文配置**：设置渲染上下文属性
- **分辨率设置**：横屏模式 1280x720 分辨率
- **音频引擎集成**：支持背景音乐和音效管理

#### 2. 主场景控制器 (MainScene)
<augment_code_snippet path="DigiviceColor/Source/MainScene.h" mode="EXCERPT">
````cpp
class MainScene : public ax::Scene
{
    enum class GameState { init = 0, update, pause, end, menu1, menu2 };
public:
    bool init() override;
    void update(float delta) override;
    void onEnter() override;
    void onExit() override;
    // 输入事件处理
    void onTouchesBegan(const std::vector<ax::Touch*>& touches, ax::Event* event);
    void onKeyPressed(ax::EventKeyboard::KeyCode code, ax::Event* event);
private:
    GameState _gameState = GameState::init;
    ax::EventListenerTouchAllAtOnce* _touchListener = nullptr;
    ax::EventListenerKeyboard* _keyboardListener = nullptr;
    ax::EventListenerMouse* _mouseListener = nullptr;
};
````
</augment_code_snippet>

**核心功能**：
- **游戏状态管理**：六种状态（初始化、更新、暂停、结束、菜单1、菜单2）
- **多输入支持**：触摸、键盘、鼠标事件处理
- **场景生命周期**：完整的进入/退出管理
- **Inspector集成**：实时调试和场景检查工具

## 🚀 功能特性分析

### 已实现功能

#### 🎮 核心游戏系统
- **横屏游戏界面**：1280x720分辨率，适配现代设备
- **游戏状态管理**：完整的状态机系统
  ```cpp
  enum class GameState {
      init = 0,    // 初始化状态
      update,      // 游戏更新状态
      pause,       // 暂停状态
      end,         // 结束状态
      menu1,       // 菜单状态1
      menu2        // 菜单状态2
  };
  ```
- **场景管理**：基于Axmol的场景切换和生命周期管理

#### 🎯 输入系统
- **多平台输入支持**：
  - ✅ **触摸输入**：完整的多点触控支持
  - ✅ **键盘输入**：全键盘事件处理
  - ⚠️ **鼠标输入**：代码已实现但暂未激活
- **事件处理机制**：
  <augment_code_snippet path="DigiviceColor/Source/MainScene.cpp" mode="EXCERPT">
  ````cpp
  void MainScene::onTouchesBegan(const std::vector<ax::Touch*>& touches, ax::Event* event)
  {
      for (auto&& t : touches) {
          // 触摸开始事件处理
      }
  }

  void MainScene::onKeyPressed(EventKeyboard::KeyCode code, Event* event)
  {
      AXLOGD("Scene: #{} onKeyPressed, keycode: {}", _sceneID, static_cast<int>(code));
  }
  ````
  </augment_code_snippet>

#### 🎨 用户界面
- **开始菜单**：包含背景图像和游戏标题
- **基础UI组件**：关闭按钮和菜单项
- **安全区域显示**：红色矩形标记，确保UI在各设备上正确显示
- **字体系统**：支持TTF字体渲染

#### 🔧 开发工具
- **Inspector调试工具**：
  - CMake选项：`AX_ENABLE_EXT_INSPECTOR=ON`
  - 实时场景检查和节点属性编辑
  - 性能监控和调试信息
- **FPS显示**：开发模式下的性能统计
- **日志系统**：完整的调试日志输出

#### 🎵 音频系统
- **音频引擎集成**：`USE_AUDIO_ENGINE=1`
- **生命周期管理**：自动处理后台/前台音频暂停/恢复

### 🔮 开发路线图

#### 第一阶段：核心系统 (当前阶段)
- [x] 基础框架搭建
- [x] 跨平台构建系统
- [x] 输入事件处理
- [x] 基础UI系统
- [ ] 数码宝贝数据模型
- [ ] 基础养成逻辑

#### 第二阶段：游戏玩法
- [ ] **虚拟宠物系统**：
  - 宠物状态管理（饥饿、快乐、健康、疲劳）
  - 宠物行为AI和动画
  - 宠物互动机制
- [ ] **进化系统**：
  - 多样化进化路线
  - 进化条件和触发机制
  - 进化动画效果
- [ ] **小游戏模块**：
  - 训练小游戏
  - 战斗系统
  - 奖励机制

#### 第三阶段：内容扩展
- [ ] **多语言支持**：国际化框架
- [ ] **数据持久化**：存档系统
- [ ] **音效和音乐**：完整音频体验
- [ ] **高级UI**：动画和特效

#### 第四阶段：优化和发布
- [ ] **性能优化**：各平台性能调优
- [ ] **用户体验**：界面优化和易用性改进
- [ ] **测试和调试**：全平台测试
- [ ] **发布准备**：打包和分发

## 🛠️ 技术实现分析

### 编程语言和版本
- **主要语言**：C++17
- **构建系统**：CMake 3.20+
- **标准库**：STL + Axmol扩展

### 第三方依赖库
<augment_code_snippet path="DigiviceColor/CMakeLists.txt" mode="EXCERPT">
````cmake
# 启用Inspector扩展
option(AX_ENABLE_EXT_INSPECTOR "Enable Inspector extension" ON)
if(AX_ENABLE_EXT_INSPECTOR)
    target_link_libraries(${APP_NAME} "${CMAKE_CURRENT_SOURCE_DIR}/build/lib/Release/Inspector.lib")
    target_compile_definitions(${APP_NAME} PRIVATE AX_ENABLE_EXT_INSPECTOR=1)
endif()
````
</augment_code_snippet>

**核心依赖**：
- **Axmol引擎**：主要游戏引擎框架
- **OpenGL ES 3.0**：图形渲染API
- **Inspector扩展**：调试和开发工具
- **音频引擎**：内置音频处理系统

### 平台特定配置

#### Windows平台
<augment_code_snippet path="DigiviceColor/proj.win32/main.cpp" mode="EXCERPT">
````cpp
#define USE_WIN32_CONSOLE  // 启用控制台调试
int WINAPI _tWinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPTSTR lpCmdLine, int nCmdShow)
{
    auto result = axmol_main();
    return result;
}
````
</augment_code_snippet>

#### Android平台
<augment_code_snippet path="DigiviceColor/proj.android/app/AndroidManifest.xml" mode="EXCERPT">
````xml
<activity
    android:name="dev.axmol.app.AppActivity"
    android:screenOrientation="landscape"
    android:configChanges="orientation|keyboardHidden|screenSize"
    android:theme="@android:style/Theme.NoTitleBar.Fullscreen">
</activity>
````
</augment_code_snippet>

## 🔧 构建和部署指南

### 环境要求
| 平台 | 编译器 | 最低版本 | 备注 |
|------|--------|----------|------|
| Windows | Visual Studio | 2019+ | 推荐使用最新版本 |
| macOS | Xcode | 12.0+ | 支持iOS和macOS构建 |
| Linux | GCC/Clang | GCC 9+ / Clang 10+ | 需要OpenGL开发库 |
| Android | NDK | r21+ | 通过Android Studio |

### 快速开始

#### 1. 环境准备
```bash
# 安装CMake (3.20+)
# Windows: 下载CMake安装包
# macOS: brew install cmake
# Linux: sudo apt-get install cmake

# 克隆Axmol引擎 (如果未安装)
git clone https://github.com/axmolengine/axmol.git
```

#### 2. 构建项目
```bash
# Windows快速构建
./build.bat

# 跨平台构建
mkdir build && cd build
cmake ..
cmake --build . --config Release
```

#### 3. 运行项目
```bash
# Windows快速运行
./run.bat

# 手动运行
cd build/bin/DigiviceColor/Release
./DigiviceColor.exe
```

### 平台特定构建

#### Windows
```batch
# 使用批处理脚本
build.bat
run.bat

# 或使用Visual Studio
# 1. 生成项目文件
cmake -B build -G "Visual Studio 16 2019"
# 2. 打开解决方案
start build/DigiviceColor.sln
```

#### macOS/iOS
```bash
# macOS构建
cmake -B build -G Xcode
xcodebuild -project build/DigiviceColor.xcodeproj -configuration Release

# iOS构建
cmake -B build -G Xcode -DCMAKE_TOOLCHAIN_FILE=ios.toolchain.cmake
```

#### Android
```bash
# 使用Android Studio打开proj.android目录
# 或使用Gradle命令行
cd proj.android
./gradlew assembleDebug
```

#### Linux
```bash
# 安装依赖
sudo apt-get install build-essential libgl1-mesa-dev libglu1-mesa-dev

# 构建
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
make -j$(nproc)
```

## 📊 代码质量评估

### 代码风格和规范
- **命名约定**：遵循Axmol引擎的命名规范
  - 类名：PascalCase (如 `MainScene`, `AppDelegate`)
  - 成员变量：下划线前缀 (如 `_gameState`, `_touchListener`)
  - 函数名：camelCase (如 `onTouchesBegan`, `menuCloseCallback`)
- **代码组织**：清晰的头文件和实现文件分离
- **注释质量**：关键功能有详细的中文注释

### 错误处理和异常管理
<augment_code_snippet path="DigiviceColor/Source/MainScene.cpp" mode="EXCERPT">
````cpp
static void problemLoading(const char* filename)
{
    printf("Error while loading: %s\n", filename);
    printf("Depending on how you compiled you might have to add 'Content/' in front of filenames\n");
}

auto label = Label::createWithTTF("DigiviceColor", "fonts/Marker Felt.ttf", 24);
if (label == nullptr) {
    problemLoading("'fonts/Marker Felt.ttf'");
}
````
</augment_code_snippet>

**错误处理特点**：
- ✅ **资源加载检查**：对关键资源进行空指针检查
- ✅ **调试信息输出**：使用AXLOG系列宏进行日志记录
- ⚠️ **异常处理**：目前主要依赖引擎的错误处理机制
- 🔄 **改进建议**：可以添加更完善的异常处理和错误恢复机制

### 性能优化分析
- **渲染优化**：
  - 使用Axmol的批量渲染机制
  - 合理的纹理管理和缓存
- **内存管理**：
  - 基于引用计数的自动内存管理
  - 事件监听器的正确清理
- **更新循环**：60FPS的固定帧率设置

## 🎓 Axmol引擎开发指南

### 核心架构概念

#### 🎬 Director（导演）
游戏的核心控制器，管理整个游戏的生命周期：
```cpp
auto director = Director::getInstance();
director->setStatsDisplay(true);  // 显示FPS
director->setAnimationInterval(1.0f / 60);  // 设置60FPS
```

#### 🎭 Scene（场景）
游戏画面的基本容器，所有可视元素都必须添加到场景中：
```cpp
class MainScene : public ax::Scene {
    bool init() override;
    void update(float delta) override;
};
```

#### 🌳 Node（节点）
场景图的基本元素，支持层次结构和变换：
- **Sprite**：显示图像
- **Label**：显示文本
- **DrawNode**：绘制几何图形

### 开发流程与建议

1. **项目构建 (CMake)**:
   * 使用 CMake 管理项目构建。`CMakeLists.txt` 定义项目配置。
   * 确保安装 C++ 编译器、CMake 及 Axmol 引擎和依赖。
   * 通过 CMake 生成平台相关的构建文件（如 `.sln`）并编译。

2. **场景管理**:
   * 为不同游戏状态创建独立的 `Scene` 子类。
   * 使用 `Director::getInstance()->pushScene()`, `popScene()`, `replaceScene()` 进行场景切换。

3. **资源管理**:
   * 将图像、音频等资源放在 `Content/` 目录。
   * 利用 Axmol 的资源缓存机制（如 `SpriteFrameCache`, `TextureCache`）优化加载。

4. **节点与层级**:
   * 使用 `addChild(node)` 添加子节点。
   * 通过 `z-order` 控制绘制顺序。
   * 使用 `getChildByName()` 或 `getChildByTag()` 查找节点。

5. **事件处理**:
   * 创建对应的 `EventListener` (如 `EventListenerTouchOneByOne`)。
   * 实现事件回调函数。
   * 通过 `_eventDispatcher->addEventListenerWithSceneGraphPriority(listener, this)` 注册监听器。

6. **UI 开发**:
   * 利用 Axmol 的 UI 组件 (`Button`, `Menu`, `ScrollView` 等)。
   * 可以使用布局系统（如 `Layout`, `HBox`, `VBox`）管理复杂界面。

7. **C++ 实践**:
   * 推荐使用现代 C++ 特性（智能指针 `std::unique_ptr`, `ax::RefPtr`；`auto`；范围 `for`；lambda）。
   * 遵循 Axmol 的编码风格指南。

8. **调试技巧**:
   * 项目集成了 Inspector 工具 (默认启用，详见“当前实现功能”部分的说明) 进行实时场景检查。
   * 使用 `AXLOG(...)` 打印调试信息到控制台。
   * 已启用FPS显示(`director->setStatsDisplay(true)`)。

9. **扩展**:
   * 可以集成其他第三方库来增强功能。



### 🛠️ 开发最佳实践

#### 1. 事件处理系统
```cpp
// 创建触摸事件监听器
_touchListener = EventListenerTouchAllAtOnce::create();
_touchListener->onTouchesBegan = AX_CALLBACK_2(MainScene::onTouchesBegan, this);
_eventDispatcher->addEventListenerWithFixedPriority(_touchListener, 10);

// 键盘事件处理
_keyboardListener = EventListenerKeyboard::create();
_keyboardListener->onKeyPressed = AX_CALLBACK_2(MainScene::onKeyPressed, this);
_eventDispatcher->addEventListenerWithFixedPriority(_keyboardListener, 11);
```

#### 2. 资源管理策略
```cpp
// 预加载纹理
Director::getInstance()->getTextureCache()->addImage("startMenu.png");

// 创建精灵并设置属性
auto sprite = Sprite::create("startMenu.png");
sprite->setPosition(Vec2(visibleSize.width / 2, visibleSize.height / 2));
sprite->setContentSize(visibleSize);  // 适应屏幕尺寸
this->addChild(sprite, 0);
```

#### 3. 场景生命周期管理
```cpp
void MainScene::onEnter() {
    Scene::onEnter();
    // 启用Inspector调试工具
    ax::extension::Inspector::getInstance()->openForScene(this);
}

void MainScene::onExit() {
    // 清理资源和监听器
    ax::extension::Inspector::getInstance()->close();
    Scene::onExit();
}
```

#### 4. 游戏状态管理
```cpp
void MainScene::update(float delta) {
    switch (_gameState) {
        case GameState::init:
            // 初始化逻辑
            _gameState = GameState::update;
            break;
        case GameState::update:
            // 游戏主循环逻辑
            break;
        case GameState::pause:
            // 暂停状态处理
            break;
    }
}
```

### 🔧 调试和开发工具

#### Inspector工具使用
- **实时场景检查**：查看节点层次结构
- **属性编辑**：实时修改节点属性
- **性能监控**：FPS和内存使用情况
- **启用方式**：CMake选项 `AX_ENABLE_EXT_INSPECTOR=ON`

#### 日志系统
```cpp
AXLOGD("Debug信息: {}", value);     // 调试日志
AXLOG("一般信息: {}", message);     // 普通日志
AXLOGE("错误信息: {}", error);      // 错误日志
```

## 📦 项目依赖

### 核心依赖
| 组件 | 版本要求 | 用途 |
|------|----------|------|
| **C++编译器** | C++17支持 | 代码编译 |
| **CMake** | 3.20+ | 构建系统 |
| **Axmol引擎** | 最新版本 | 游戏引擎框架 |
| **OpenGL** | ES 3.0+ | 图形渲染 |

### 平台特定依赖
- **Windows**: Visual Studio 2019+, Windows SDK
- **macOS**: Xcode 12.0+, macOS 10.15+
- **Linux**: GCC 9+/Clang 10+, OpenGL开发库
- **Android**: NDK r21+, Android SDK API 21+
- **iOS**: Xcode 12.0+, iOS 11.0+

### 第三方库
- **音频引擎**: 内置于Axmol引擎
- **Inspector扩展**: 调试和开发工具
- **物理引擎**: Box2D (可选)
- **网络库**: 内置网络支持

## 📄 许可证

本项目采用 **MIT许可证**，与Axmol引擎保持一致。

```
MIT License

Copyright (c) 2024 DigiviceColor Project

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.
```

## 🤝 贡献指南

欢迎对DigiviceColor项目做出贡献！请遵循以下步骤：

1. **Fork项目** 并创建功能分支
2. **编写代码** 并遵循项目的编码规范
3. **添加测试** 确保新功能正常工作
4. **提交Pull Request** 并详细描述更改内容

### 开发环境设置
```bash
# 1. 克隆项目
git clone <repository-url>
cd DigiviceColor

# 2. 安装Axmol引擎
# 请参考Axmol官方文档

# 3. 构建项目
mkdir build && cd build
cmake ..
cmake --build .
```

## 📞 联系方式

- **项目主页**: [GitHub Repository]
- **问题反馈**: [GitHub Issues]
- **技术讨论**: [GitHub Discussions]
- **Axmol引擎**: https://github.com/axmolengine/axmol

---

**DigiviceColor** - 重现经典数码宝贝体验的现代化跨平台游戏 🎮✨
